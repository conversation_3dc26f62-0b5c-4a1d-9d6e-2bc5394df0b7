import api from './api';

/**
 * Admin Order Service
 * Handles all admin-related order operations
 */
const adminOrderService = {
  /**
   * Get all orders with filtering, sorting, and pagination
   * @param {Object} params - Query parameters
   * @returns {Promise} Promise with orders data
   */
  getAllOrders: async (params = {}) => {
    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      paymentStatus = '',
      orderType = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = '',
      minAmount = '',
      maxAmount = '',
      buyerId = '',
      sellerId = ''
    } = params;

    const queryParams = new URLSearchParams();
    
    // Add all parameters to query string
    queryParams.append('page', page);
    queryParams.append('limit', limit);
    if (search) queryParams.append('search', search);
    if (status && status !== 'all') queryParams.append('status', status);
    if (paymentStatus && paymentStatus !== 'all') queryParams.append('paymentStatus', paymentStatus);
    if (orderType && orderType !== 'all') queryParams.append('orderType', orderType);
    if (sortBy) queryParams.append('sortBy', sortBy);
    if (sortOrder) queryParams.append('sortOrder', sortOrder);
    if (dateFrom) queryParams.append('dateFrom', dateFrom);
    if (dateTo) queryParams.append('dateTo', dateTo);
    if (minAmount) queryParams.append('minAmount', minAmount);
    if (maxAmount) queryParams.append('maxAmount', maxAmount);
    if (buyerId) queryParams.append('buyerId', buyerId);
    if (sellerId) queryParams.append('sellerId', sellerId);

    const response = await api.get(`/admin/orders?${queryParams.toString()}`);
    return response.data;
  },

  /**
   * Get single order by ID
   * @param {string} id - Order ID
   * @returns {Promise} Promise with order data
   */
  getOrderById: async (id) => {
    const response = await api.get(`/admin/orders/${id}`);
    return response.data;
  },

  /**
   * Update order status
   * @param {string} id - Order ID
   * @param {Object} statusData - Status update data
   * @returns {Promise} Promise with updated order data
   */
  updateOrderStatus: async (id, statusData) => {
    const response = await api.put(`/admin/orders/${id}/status`, statusData);
    return response.data;
  },

  /**
   * Delete order
   * @param {string} id - Order ID
   * @returns {Promise} Promise with deletion result
   */
  deleteOrder: async (id) => {
    const response = await api.delete(`/admin/orders/${id}`);
    return response.data;
  },

  /**
   * Bulk update orders
   * @param {Array} orderIds - Array of order IDs
   * @param {string} status - New status
   * @returns {Promise} Promise with bulk update result
   */
  bulkUpdateOrders: async (orderIds, status) => {
    const response = await api.post('/admin/orders/bulk-update', {
      orderIds,
      status
    });
    return response.data;
  },

  /**
   * Bulk delete orders
   * @param {Array} orderIds - Array of order IDs
   * @returns {Promise} Promise with bulk delete result
   */
  bulkDeleteOrders: async (orderIds) => {
    const response = await api.post('/admin/orders/bulk-delete', {
      orderIds
    });
    return response.data;
  },

  /**
   * Get order statistics
   * @returns {Promise} Promise with order stats
   */
  getOrderStats: async () => {
    const response = await api.get('/admin/orders/stats');
    return response.data;
  },

  /**
   * Export orders data
   * @param {Object} params - Export parameters
   * @returns {Promise} Promise with export data
   */
  exportOrders: async (params = {}) => {
    const {
      format = 'csv',
      status = '',
      dateFrom = '',
      dateTo = ''
    } = params;

    const queryParams = new URLSearchParams();
    queryParams.append('format', format);
    if (status && status !== 'all') queryParams.append('status', status);
    if (dateFrom) queryParams.append('dateFrom', dateFrom);
    if (dateTo) queryParams.append('dateTo', dateTo);

    const response = await api.get(`/admin/orders/export?${queryParams.toString()}`);
    return response.data;
  },

  /**
   * Process refund for order
   * @param {string} id - Order ID
   * @param {Object} refundData - Refund data
   * @returns {Promise} Promise with refund result
   */
  processRefund: async (id, refundData) => {
    const response = await api.post(`/admin/orders/${id}/refund`, refundData);
    return response.data;
  },

  /**
   * Get order timeline
   * @param {string} id - Order ID
   * @returns {Promise} Promise with order timeline
   */
  getOrderTimeline: async (id) => {
    const response = await api.get(`/admin/orders/${id}/timeline`);
    return response.data;
  },

  /**
   * Flag order
   * @param {string} id - Order ID
   * @param {Object} flagData - Flag data
   * @returns {Promise} Promise with flag result
   */
  flagOrder: async (id, flagData) => {
    const response = await api.put(`/admin/orders/${id}/flag`, flagData);
    return response.data;
  },

  /**
   * Unflag order
   * @param {string} id - Order ID
   * @returns {Promise} Promise with unflag result
   */
  unflagOrder: async (id) => {
    const response = await api.put(`/admin/orders/${id}/unflag`);
    return response.data;
  },

  /**
   * Get order analytics
   * @param {Object} params - Analytics parameters
   * @returns {Promise} Promise with analytics data
   */
  getOrderAnalytics: async (params = {}) => {
    const {
      period = '30d',
      groupBy = 'day'
    } = params;

    const queryParams = new URLSearchParams();
    queryParams.append('period', period);
    queryParams.append('groupBy', groupBy);

    const response = await api.get(`/admin/orders/analytics?${queryParams.toString()}`);
    return response.data;
  }
};

export default adminOrderService;
